#!/usr/bin/env python3
"""
SOC Automation Suite - CLI Entry Point
======================================
A comprehensive security automation toolkit that integrates:
- Network reconnaissance and scanning
- File integrity monitoring
- Threat intelligence enrichment
- Automated reporting
- Task scheduling

Author: Quang-Ng-Duong
Version: 1.0
"""

import json
import argparse
import logging
from soc_automation.suite import SOCAutomationSuite

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',  # cspell:disable-line
    handlers=[
        logging.FileHandler('soc_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(description="SOC Automation Suite")
    parser.add_argument("--scan", help="Scan a single target")
    parser.add_argument("--create-baseline", help="Create file integrity baseline for directory")
    parser.add_argument("--check-baseline", action="store_true", 
                       help="Check files against baseline")
    parser.add_argument("--monitor", action="store_true", 
                       help="Start continuous monitoring")
    parser.add_argument("--threat-intel", help="Check threat intelligence for IP")
    
    args = parser.parse_args()
    
    suite = SOCAutomationSuite()
    
    if args.scan:
        result = suite.scanner.comprehensive_scan(args.scan)
        print(json.dumps(result, indent=2))
    
    elif args.create_baseline:
        suite.file_monitor.create_baseline(args.create_baseline)
    
    elif args.check_baseline:
        changes = suite.file_monitor.compare_baseline()
        if changes:
            print(json.dumps(changes, indent=2))
    
    elif args.threat_intel:
        threat_data = suite.threat_intel.check_ip_reputation(args.threat_intel)
        print(json.dumps(threat_data, indent=2))
    
    elif args.monitor:
        suite.start_monitoring()
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()