"""
SOC Automation Suite
===================
A comprehensive security automation toolkit that integrates:
- Network reconnaissance and scanning
- File integrity monitoring
- Threat intelligence enrichment
- Automated reporting
- Task scheduling

Author: Quang-<PERSON>-<PERSON>ng
Version: 1.0
"""

__version__ = "1.0.0"
__author__ = "Quang-Ng-Duong"

from .scanner import NetworkScanner
from .threat_intel import ThreatIntelligence
from .file_monitor import FileIntegrityMonitor
from .reporter import ReportGenerator
from .suite import SOCAutomationSuite

__all__ = [
    'NetworkScanner',
    'ThreatIntelligence', 
    'FileIntegrityMonitor',
    'ReportGenerator',
    'SOCAutomationSuite'
]
