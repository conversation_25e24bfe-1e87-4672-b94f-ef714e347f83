#!/usr/bin/env python3
"""
SOC Automation Suite Demo
=========================
Demonstration script showing various features of the SOC Automation Suite.
"""
# cspell:ignore ipinfo

import json
import time
from soc_automation import SOCAutomationSuite

def demo_network_scanning():
    """Demonstrate network scanning capabilities"""
    print("=" * 60)
    print("NETWORK SCANNING DEMO")
    print("=" * 60)
    
    suite = SOCAutomationSuite()
    
    # Test targets
    targets = ["*******", "*******", "google.com"]
    
    for target in targets:
        print(f"\n🔍 Scanning {target}...")
        result = suite.scanner.comprehensive_scan(target)
        
        print(f"✅ Target: {result['target']}")
        print(f"📡 Reachable: {result['reachable']}")
        print(f"🔓 Open Ports: {result['open_ports']}")
        
        if result['banners']:
            print("🏷️  Service Banners:")
            for port, banner in result['banners'].items():
                print(f"   Port {port}: {banner[:50]}...")
        
        time.sleep(1)  # Be nice to the targets

def demo_threat_intelligence():
    """Demonstrate threat intelligence capabilities"""
    print("\n" + "=" * 60)
    print("THREAT INTELLIGENCE DEMO")
    print("=" * 60)
    
    suite = SOCAutomationSuite()
    
    # Test IPs
    test_ips = ["*******", "*******", "**************"]
    
    for ip in test_ips:
        print(f"\n🕵️  Checking reputation for {ip}...")
        threat_data = suite.threat_intel.check_ip_reputation(ip)
        
        print(f"📍 IP: {threat_data['ip']}")
        
        if 'ipinfo' in threat_data['sources']:
            info = threat_data['sources']['ipinfo']
            print(f"🌍 Location: {info.get('city', 'Unknown')}, {info.get('country', 'Unknown')}")
            print(f"🏢 Organization: {info.get('org', 'Unknown')}")
        
        time.sleep(1)  # Rate limiting

def demo_file_monitoring():
    """Demonstrate file monitoring capabilities"""
    print("\n" + "=" * 60)
    print("FILE MONITORING DEMO")
    print("=" * 60)
    
    suite = SOCAutomationSuite()
    
    # Create a test directory
    import os
    test_dir = "test_monitor"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # Create a test file
    test_file = os.path.join(test_dir, "test.txt")
    with open(test_file, "w") as f:
        f.write("This is a test file for monitoring.")
    
    print(f"📁 Creating baseline for {test_dir}...")
    suite.file_monitor.watch_path = test_dir
    suite.file_monitor.create_baseline(test_dir)
    
    print("✅ Baseline created successfully!")
    
    # Modify the file
    print("📝 Modifying test file...")
    with open(test_file, "a") as f:
        f.write("\nThis line was added after baseline.")
    
    # Check for changes
    print("🔍 Checking for changes...")
    changes = suite.file_monitor.compare_baseline()
    
    if changes:
        print(f"⚠️  Changes detected:")
        print(f"   Modified: {len(changes['modified'])} files")
        print(f"   Added: {len(changes['added'])} files")
        print(f"   Deleted: {len(changes['deleted'])} files")
        
        if changes['modified']:
            print("   Modified files:")
            for file in changes['modified']:
                print(f"     - {file}")
    
    # Cleanup
    import shutil
    shutil.rmtree(test_dir)
    if os.path.exists("file_baseline.json"):
        os.remove("file_baseline.json")
    print("🧹 Cleanup completed!")

def demo_reporting():
    """Demonstrate reporting capabilities"""
    print("\n" + "=" * 60)
    print("REPORTING DEMO")
    print("=" * 60)
    
    suite = SOCAutomationSuite()
    
    # Generate some scan results
    print("🔍 Performing scans for report...")
    scan_results = []
    targets = ["*******", "*******"]
    
    for target in targets:
        result = suite.scanner.comprehensive_scan(target)
        scan_results.append(result)
        time.sleep(1)
    
    # Generate reports
    print("📊 Generating JSON report...")
    json_report = suite.reporter.generate_scan_report(scan_results, "json")
    print(f"✅ JSON report saved: {json_report}")
    
    print("📊 Generating HTML report...")
    html_report = suite.reporter.generate_scan_report(scan_results, "html")
    print(f"✅ HTML report saved: {html_report}")

def main():
    """Run all demos"""
    print("🚀 SOC Automation Suite Demo")
    print("This demo will showcase the main features of the suite.")
    print("Press Ctrl+C to stop at any time.\n")
    
    try:
        demo_network_scanning()
        demo_threat_intelligence()
        demo_file_monitoring()
        demo_reporting()
        
        print("\n" + "=" * 60)
        print("✅ DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("Check the 'reports' directory for generated reports.")
        print("Check 'soc_automation.log' for detailed logs.")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo stopped by user.")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")

if __name__ == "__main__":
    main()
