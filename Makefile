# SOC Automation Suite Makefile
# =============================

.PHONY: help install test demo clean lint format setup dev-install

# Default target
help:
	@echo "SOC Automation Suite - Available Commands:"
	@echo ""
	@echo "  setup          - Initial project setup"
	@echo "  install        - Install dependencies"
	@echo "  dev-install    - Install in development mode"
	@echo "  test           - Run tests"
	@echo "  demo           - Run demo script"
	@echo "  lint           - Run code linting"
	@echo "  format         - Format code"
	@echo "  clean          - Clean up generated files"
	@echo "  scan TARGET    - Scan a target (e.g., make scan TARGET=*******)"
	@echo "  threat IP      - Check threat intel (e.g., make threat IP=*******)"
	@echo ""

# Initial setup
setup:
	@echo "Setting up SOC Automation Suite..."
	python -m pip install --upgrade pip
	pip install -r requirements.txt
	@echo "Setup complete!"

# Install dependencies
install:
	pip install -r requirements.txt

# Install in development mode
dev-install:
	pip install -e .

# Run demo
demo:
	python demo.py

# Run tests (placeholder for future tests)
test:
	@echo "Running tests..."
	python -c "from soc_automation import SOCAutomationSuite; print('✅ Import test passed')"
	python main.py --help
	@echo "✅ All tests passed!"

# Scan a target
scan:
	@if [ -z "$(TARGET)" ]; then \
		echo "Usage: make scan TARGET=<target>"; \
		echo "Example: make scan TARGET=*******"; \
	else \
		python main.py --scan $(TARGET); \
	fi

# Check threat intelligence
threat:
	@if [ -z "$(IP)" ]; then \
		echo "Usage: make threat IP=<ip_address>"; \
		echo "Example: make threat IP=*******"; \
	else \
		python main.py --threat-intel $(IP); \
	fi

# Code linting (if flake8 is installed)
lint:
	@if command -v flake8 >/dev/null 2>&1; then \
		echo "Running flake8..."; \
		flake8 soc_automation/ main.py demo.py; \
	else \
		echo "flake8 not installed. Install with: pip install flake8"; \
	fi

# Code formatting (if black is installed)
format:
	@if command -v black >/dev/null 2>&1; then \
		echo "Formatting code with black..."; \
		black soc_automation/ main.py demo.py; \
	else \
		echo "black not installed. Install with: pip install black"; \
	fi

# Clean up generated files
clean:
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true  # Remove Python __pycache__ directories (standard Python cache dir name)
	rm -f soc_automation.log
	rm -f file_baseline.json
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	@echo "Cleanup complete!"

# Create baseline for current directory
baseline:
	python main.py --create-baseline .

# Check baseline
check-baseline:
	python main.py --check-baseline

# Start monitoring
monitor:
	python main.py --monitor
